"use client";

import { ReactNode } from "react";
import classNames from "classnames";

export interface DrawerProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  title?: ReactNode;
  children?: ReactNode;
  className?: string;
}

export const Drawer = ({
  open,
  onOpenChange,
  title,
  children,
  className,
}: DrawerProps) => {
  return (
    <div
      className={classNames(
        "fixed inset-0 z-50 bg-black/50 transition-opacity duration-300",
        {
          "opacity-100 visible": open,
          "opacity-0 invisible": !open,
        },
      )}
      onClick={() => onOpenChange?.(false)}
    >
      <div
        onClick={(e) => e.stopPropagation()}
        data-state={open ? "open" : "closed"}
        className={classNames(
          "bg-background",
          "fixed right-0 top-0 h-full w-full max-w-full md:w-[872px]",
          "transition-transform duration-300 ease-in-out",
          className,
        )}
        style={{
          transform: open ? "translateX(0)" : "translateX(100%)",
        }}
      >
        {title && (
          <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-3 text-lg font-semibold z-10">
            {title}
          </div>
        )}
        <div className="p-6">{children}</div>
      </div>
    </div>
  );
};
