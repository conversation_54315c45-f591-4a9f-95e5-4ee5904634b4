"use client";
import { useQuery } from "@tanstack/react-query";
import service from "@/api";
import { LoginScreen } from "./components/login-screen";
import { redirect } from "next/navigation";

export default function Home() {
  const { data } = useQuery({
    queryFn: async () => {
      try {
        const res = await service.getPortfolioOverview();
        return res.data?.data;
      } catch {
        return undefined;
      }
    },
    queryKey: ["portfolio-overview"],
  });

  const isLogin = !!data;

  if (!isLogin) {
    return <LoginScreen />;
  }
  redirect("/dashboard");
}
