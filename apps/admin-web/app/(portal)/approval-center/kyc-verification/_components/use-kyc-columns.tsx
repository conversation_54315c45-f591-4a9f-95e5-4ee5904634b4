import { useMemo } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { KycVerificationSearchResponse } from "@/api/data-contracts";
import { CopyButton } from "@/ui-components/copy-button";
import { KycStatusBadge } from "./kyc-status-badge";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { AppMenu, type AppMenuItemProps } from "@repo/ui/app-menu";
import type { KycSubmission } from "./kyc-review/context";
import { formatDate } from "@/utils/date";

const columnHelper = createColumnHelper<KycVerificationSearchResponse>();

interface UseKycColumnsProps {
  onKycReview?: (value: KycSubmission) => void;
  onViewDetails?: (record: KycVerificationSearchResponse) => void;
}

export const useKycColumns = ({ onKycReview, onViewDetails }: UseKycColumnsProps = {}) => {
  return useMemo(
    () => [
      columnHelper.accessor("publicId", {
        size: 260,
        header: "Portfolio ID",
        cell: ({ getValue }) => {
          const value = getValue();
          return (
            <div className="flex gap-2 justify-between">
              <span>{value}</span>
              {value && <CopyButton text={value} />}
            </div>
          );
        },
      }),
      columnHelper.accessor("submissionId", {
        size: 200,
        header: "Submission ID",
        cell: ({ getValue }) => {
          const value = getValue();
          return <span>{value}</span>;
        },
      }),
      columnHelper.accessor("status", {
        size: 140,
        header: "Status",
        cell: ({ getValue }) => {
          const status = getValue();
          return <KycStatusBadge status={status} />;
        },
      }),
      columnHelper.accessor("email", {
        size: 300,
        header: "Email",
        cell: ({ getValue }) => {
          const value = getValue();
          return <span>{value}</span>;
        },
      }),
      columnHelper.accessor("submissionDate", {
        size: 200,
        header: "Submitted date",
        cell: ({ getValue }) => {
          const value = getValue();
          return <span>{formatDate(value)}</span>;
        },
      }),
      columnHelper.accessor("actionReason", {
        size: 300,
        header: "Rejection reasons",
        cell: ({ row }) => {
          const status = row.original.status;
          const actionReason = row.original.actionReason;

          if (status === "REJECTED" || status === "RESTRICTED") {
            const displayReason = actionReason
              ?.replace(/_/g, " ")
              .toLowerCase()
              .replace(/\b\w/g, (l) => l.toUpperCase());
            return <span>{displayReason || "-"}</span>;
          }
          return "-";
        },
      }),
      columnHelper.display({
        size: 80,
        id: "action",
        header: "Action",
        cell: ({ row }) => {
          const submissionId = row.original.submissionId;
          const status = row.original.status;
          const record = row.original;

          const menuConfig: AppMenuItemProps[] = [];

          // Always add "View details" option
          if (onViewDetails) {
            menuConfig.push({
              key: "VIEW_DETAILS",
              label: "View details",
              onClick: () => {
                onViewDetails(record);
              },
            });
          }

          if (status === "PENDING" && submissionId && onKycReview) {
            menuConfig.push(
              {
                key: "APPROVE",
                label: "Approve",
                onClick: () => {
                  onKycReview({ submissionId, action: "APPROVE" });
                },
              },
              {
                key: "REJECT",
                label: "Reject",
                color: "red",
                onClick: () => {
                  onKycReview({ submissionId, action: "REJECT" });
                },
              },
              {
                key: "RESTRICT",
                label: "Restrict",
                color: "red",
                onClick: () => {
                  onKycReview({ submissionId, action: "RESTRICT" });
                },
              },
            );
          }

          if (menuConfig.length === 0) {
            return (
              <div className="flex justify-center">
                <Button variant="ghost" m="auto" color="gray" size="1" disabled>
                  <DotsHorizontalIcon />
                </Button>
              </div>
            );
          }

          return (
            <div className="flex justify-center">
              <AppMenu config={menuConfig}>
                <Button variant="ghost" m="auto" color="gray" size="1">
                  <DotsHorizontalIcon />
                </Button>
              </AppMenu>
            </div>
          );
        },
      }),
    ],
    [onKycReview, onViewDetails],
  );
};
