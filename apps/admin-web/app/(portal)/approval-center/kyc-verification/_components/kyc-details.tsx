import { Text, Separator, Button } from "@radix-ui/themes";
import { DetailGrid, DetailItem } from "@/ui-components/detail-grid";
import { KycStatusBadge } from "./kyc-status-badge";
import { KycAuditLogTable } from "./kyc-audit-log-table";
import type { KycSubmission } from "./kyc-review/context";
import type { KycVerificationSearchResponse } from "@/api/data-contracts";
import { formatDate } from "@/utils/date";
import { OpenMediaButton } from "@/app/components/media-dialog/open-media";

export const KycDetailsHeader = ({
  data,
  onKycReview,
}: {
  data: KycVerificationSearchResponse;
  onKycReview: (submission: KycSubmission | null) => void;
}) => {
  const { submissionId, email, publicId, status } = data;

  if (!submissionId) {
    return null;
  }

  const isPending = status === "PENDING";

  const handleReject = () => {
    onKycReview({ submissionId, action: "REJECT" });
  };

  const handleRestrict = () => {
    onKycReview({ submissionId, action: "RESTRICT" });
  };

  const handleApprove = () => {
    onKycReview({ submissionId, action: "APPROVE" });
  };

  return (
    <div className="flex gap-6 justify-between">
      <div>
        <div className="grow flex items-center gap-2 mb-1">
          <Text size="4" weight="bold">
            {email}
          </Text>
          <KycStatusBadge status={status} />
        </div>
        <Text size="1" color="gray" weight="light" highContrast>
          Portfolio ID: {publicId}
        </Text>
      </div>
      {isPending && (
        <div className="flex gap-3">
          <Button
            size="1"
            variant="soft"
            radius="full"
            color="red"
            highContrast
            onClick={handleRestrict}
          >
            Restrict
          </Button>
          <Button
            size="1"
            variant="soft"
            radius="full"
            color="red"
            highContrast
            onClick={handleReject}
          >
            Reject
          </Button>
          <Button
            size="1"
            variant="soft"
            radius="full"
            color="gray"
            highContrast
            onClick={handleApprove}
          >
            Approve
          </Button>
        </div>
      )}
    </div>
  );
};

const renderBasicInfo = (data: KycVerificationSearchResponse) => {
  const {
    submissionId,
    email,
    firstName,
    lastName,
    nationality,
    countryOfResidence,
    identityDocumentType,
    submissionDate,
  } = data;

  const displayDocumentType = identityDocumentType
    ?.replace(/_/g, "/")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return (
    <DetailGrid columns={4}>
      <DetailItem
        label="Submission ID"
        value={submissionId || "-"}
        enableCopy={!!submissionId}
        copyText={submissionId?.toString()}
      />
      <DetailItem
        label="Email"
        value={email || "-"}
        enableCopy={!!email}
        copyText={email}
      />
      <DetailItem label="First name" value={firstName || "-"} />
      <DetailItem label="Last name" value={lastName || "-"} />
      <DetailItem label="Nationality" value={nationality || "-"} />
      <DetailItem
        label="Country of residence"
        value={countryOfResidence || "-"}
      />
      <DetailItem label="ID type" value={displayDocumentType || "-"} />
      <DetailItem label="Submitted date" value={formatDate(submissionDate)} />
    </DetailGrid>
  );
};

const renderDocuments = (data: KycVerificationSearchResponse) => {
  const { documentFrontKey, documentBackKey, selfieKey, identityDocumentType } =
    data;

  const displayDocumentType = identityDocumentType
    ?.replace(/_/g, "/")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase());

  return (
    <>
      <Separator size="4" />
      <div className="flex flex-col gap-4">
        <Text size="3" weight="medium">
          Submitted documents
        </Text>

        <div className="flex flex-col gap-2">
          <Text size="2" color="gray">
            ID type
          </Text>
          <Text size="2">{displayDocumentType || "-"}</Text>
        </div>

        <DetailGrid columns={4}>
          <div className="flex flex-col gap-2">
            <Text size="2" color="gray">
              Uploaded documents
            </Text>
            <div className="flex gap-2">
              {documentFrontKey && (
                <OpenMediaButton url={documentFrontKey} type="image" />
              )}
              {documentBackKey && (
                <OpenMediaButton url={documentBackKey} type="image" />
              )}
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <Text size="2" color="gray">
              Selfie image
            </Text>
            <div className="flex gap-2">
              {selfieKey ? (
                <OpenMediaButton url={selfieKey} type="image" />
              ) : (
                <Text size="2">-</Text>
              )}
            </div>
          </div>
        </DetailGrid>
      </div>
    </>
  );
};

export const KycDetails = ({
  data,
}: {
  data: KycVerificationSearchResponse;
}) => {
  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div className="flex flex-col gap-2">
        <Text size="3" weight="medium">
          Basic information
        </Text>
        {renderBasicInfo(data)}
      </div>

      {renderDocuments(data)}
      <Separator size="4" />

      {/* Audit Log */}
      <div className="flex flex-col gap-2">
        <Text size="3" weight="medium">
          Audit log
        </Text>
        <KycAuditLogTable data={data} />
      </div>
    </div>
  );
};
