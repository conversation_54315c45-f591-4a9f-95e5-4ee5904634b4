"use client";
import { use, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, Head<PERSON> } from "@radix-ui/themes";
import service from "@/api";
import { KycVerificationSearchResponse } from "@/api/data-contracts";
import { SearchFilters } from "./_components/search-filters";
import { KycTable } from "./_components/kyc-table";
import { Pagination } from "@repo/ui/pagination";
import { DownloadIcon } from "@radix-ui/react-icons";
import { InfoLayout } from "@repo/ui/info-layout";
import { Drawer } from "@repo/ui/drawer";
import {
  USE_MOCK_DATA,
  mockSearchKycVerifications,
} from "./_mock/mock-service";
import { KycReviewDialogContext } from "./_components/kyc-review/context";
import { KycReviewDialogProvider } from "./_components/kyc-review";
import {
  KycDeta<PERSON>,
  KycDetailsHeader,
} from "./_components/kyc-details";
import { MediaDialogProvider } from "../../../components/media-dialog";

const Page = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedKyc, setSelectedKyc] = useState<KycVerificationSearchResponse | null>(
    null,
  );
  const [drawerOpen, setDrawerOpen] = useState(false);
  const pageSize = 10;

  const { setSubmission } = use(KycReviewDialogContext);

  // Handler for viewing KYC details
  const handleViewDetails = (record: KycVerificationSearchResponse) => {
    setSelectedKyc(record);
    setDrawerOpen(true);
  };

  // Handler for closing drawer
  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedKyc(null);
  };

  const { data, isLoading, error } = useQuery({
    queryKey: [
      "kyc-verifications",
      searchQuery,
      statusFilter,
      currentPage,
      pageSize,
    ],
    queryFn: async () => {
      const searchParams: {
        publicId?: string;
        email?: string;
        status?: "PENDING" | "APPROVED" | "REJECTED" | "RESTRICTED";
        page: number;
        pageSize: number;
      } = {
        page: currentPage,
        pageSize,
      };

      // Determine if search query is email or portfolio ID
      if (searchQuery.trim()) {
        if (searchQuery.includes("@")) {
          searchParams.email = searchQuery.trim();
        } else {
          searchParams.publicId = searchQuery.trim();
        }
      }

      // Add status filter if selected
      if (statusFilter) {
        searchParams.status = statusFilter as
          | "PENDING"
          | "APPROVED"
          | "REJECTED"
          | "RESTRICTED";
      }

      // Use mock data if enabled, otherwise use real API
      const response = USE_MOCK_DATA
        ? await mockSearchKycVerifications(searchParams)
        : await service.searchKycVerifications(searchParams);
      return response.data?.data;
    },
  });

  const verifications = data?.records || [];
  const totalPages = data?.totalPages || 1;

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleReset = () => {
    setCurrentPage(1); // Reset to first page when resetting
  };

  const handleExportData = () => {
    // TODO: Implement export functionality
    console.log("Export data functionality to be implemented");
  };

  return (
    <main className="grow">
      <div className="flex justify-between items-center mb-6">
        <Heading size="7" weight="medium">
          KYC approval
        </Heading>
        <Button
          radius="full"
          color="gray"
          highContrast
          variant="soft"
          size="2"
          onClick={handleExportData}
        >
          <DownloadIcon />
          Export data
        </Button>
      </div>

      <div className="mb-6">
        <SearchFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          statusFilter={statusFilter}
          onStatusChange={setStatusFilter}
          onSearch={handleSearch}
          onReset={handleReset}
        />
      </div>

      {error ? (
        <div className="bg-[#00000008] rounded-lg">
          <InfoLayout
            className="py-10 px-6"
            icon="/empty-file.png"
            iconAlt="no data"
            title="Error"
            description="An error occurred while fetching KYC verifications"
          />
        </div>
      ) : (
        <>
          <KycTable
            data={verifications}
            isLoading={isLoading}
            onKycReview={setSubmission}
            onViewDetails={handleViewDetails}
          />
          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              className="justify-end mt-6"
              nextText="Next"
              previousText="Previous"
            />
          )}
        </>
      )}

      {/* KYC Details Drawer */}
      <Drawer
        open={drawerOpen}
        onOpenChange={handleCloseDrawer}
        title={
          selectedKyc && (
            <KycDetailsHeader
              data={selectedKyc}
              onKycReview={setSubmission}
            />
          )
        }
      >
        {selectedKyc && <KycDetails data={selectedKyc} />}
      </Drawer>
    </main>
  );
};

export default function KycVerificationPage() {
  return (
    <KycReviewDialogProvider>
      <MediaDialogProvider>
        <Page />
      </MediaDialogProvider>
    </KycReviewDialogProvider>
  );
}
