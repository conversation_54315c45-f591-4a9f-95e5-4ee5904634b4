import { Text, Separator, Button } from "@radix-ui/themes";
import { DetailGrid, DetailItem } from "@/ui-components/detail-grid";
import { DepositStatusBadge } from "./deposit-status-badge";
import type { DepositSubmission } from "./deposit-review";
import type { DepositRecord } from "../types";
import { formatDate } from "@/utils/date";
import { AuditLogTable } from "./audit-log-table";
import { OpenMediaButton } from "@/app/components/media-dialog/open-media";

export const DepositDetailsHeader = ({
  data,
  onDepositReview,
}: {
  data: DepositRecord;
  onDepositReview: (submission: DepositSubmission | null) => void;
}) => {
  const { depositId, transactionId, userPublicId, status } = data;

  if (!depositId) {
    return null;
  }

  const isPending = status === "PENDING";

  const handleReject = () => {
    onDepositReview({ depositId, action: "REJECT" });
  };

  const handleApprove = () => {
    onDepositReview({ depositId, action: "APPROVE" });
  };

  return (
    <div className="flex gap-6 justify-between">
      <div>
        <div className="grow flex items-center gap-2 mb-1">
          <Text size="4" weight="bold">
            Transaction ID: {transactionId}
          </Text>
          <DepositStatusBadge status={status} />
        </div>
        <Text size="1" color="gray" weight="light" highContrast>
          Portfolio ID: {userPublicId}
        </Text>
      </div>
      {isPending && (
        <div className="flex gap-3">
          <Button
            size="1"
            variant="soft"
            radius="full"
            highContrast
            onClick={handleReject}
          >
            Reject
          </Button>
          <Button
            size="1"
            variant="soft"
            radius="full"
            color="gray"
            highContrast
            onClick={handleApprove}
          >
            Approve
          </Button>
        </div>
      )}
    </div>
  );
};

const renderBasicInfo = (data: DepositRecord) => {
  if (data.depositType === "FIAT") {
    const { amount, currency, senderAccount, createdAt } = data;
    return (
      <DetailGrid columns={4}>
        <DetailItem label="Deposit method" value="Bank transfer" />
        <DetailItem label="Bank name" value={senderAccount?.bankName || "-"} />
        <DetailItem
          label="Bank account number"
          value={senderAccount?.accountNumber || "-"}
          enableCopy={!!senderAccount?.accountNumber}
          copyText={senderAccount?.accountNumber}
        />
        <DetailItem
          label="Deposit amount"
          value={
            amount ? `${amount.toLocaleString()} ${currency || ""}`.trim() : "-"
          }
        />
        <DetailItem label="Deposit date" value={formatDate(createdAt)} />
      </DetailGrid>
    );
  }

  if (data.depositType === "CRYPTO") {
    const {
      amount,
      currency,
      transactionHash,
      fromAddress,
      network,
      createdAt,
    } = data;
    return (
      <DetailGrid columns={2}>
        <DetailItem label="Deposit method" value="Blockchain transfer" />
        <DetailItem label="Transaction hash" value={transactionHash || "-"} />
        <DetailItem label="Wallet address" value={fromAddress || "-"} />
        <DetailItem label="Network" value={network || "-"} />
        <DetailItem label="Deposit date" value={formatDate(createdAt)} />
        <DetailItem
          label="Deposit amount"
          value={
            amount ? `${amount.toLocaleString()} ${currency || ""}`.trim() : "-"
          }
        />
      </DetailGrid>
    );
  }
};

const renderFiles = (data: DepositRecord) => {
  if (data.depositType === "FIAT" && data.proofOfTransferFileKey) {
    return (
      <>
        <Separator size="4" />
        <div className="flex flex-col gap-2 items-start">
          <Text size="3" weight="medium">
            Submitted documents
          </Text>
          <Text size="2" color="gray">
            Uploaded documents
          </Text>
          <OpenMediaButton url={data.proofOfTransferFileKey} type="image" />
        </div>
      </>
    );
  }
};

export const DepositDetails = ({ data }: { data: DepositRecord }) => {
  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div className="flex flex-col gap-2">
        <Text size="3" weight="medium">
          Basic information
        </Text>
        {renderBasicInfo(data)}
      </div>

      {renderFiles(data)}
      <Separator size="4" />

      {/* Audit Log */}
      <div className="flex flex-col gap-2">
        <Text size="3" weight="medium">
          Audit log
        </Text>
        <AuditLogTable data={data} />
      </div>
    </div>
  );
};
