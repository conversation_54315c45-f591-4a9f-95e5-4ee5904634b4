import { Table } from "@radix-ui/themes";
import { InfoLayout } from "@repo/ui/info-layout";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useAuditLogColumns } from "./use-audit-log-columns";
import type { DepositRecord } from "../types";
import style from "./index.module.scss";
import { formatDate } from "@/utils/date";

export const AuditLogTable = ({ data }: { data: DepositRecord }) => {
  const { status, createdAt, operatorAuditDetails } = data;

  const columns = useAuditLogColumns();

  const auditData = operatorAuditDetails
    ? [
        {
          status,
          reasonDescription: operatorAuditDetails.reasonDescription,
          operatorName: operatorAuditDetails.operatorName,
          auditTime: formatDate(createdAt),
        },
      ]
    : [];

  const table = useReactTable({
    data: auditData,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (!auditData.length) {
    return (
      <div className="bg-[#00000008] rounded-lg">
        <InfoLayout
          className="py-10 px-6"
          title="No records found"
          icon="/empty-file.png"
          iconAlt="no data"
        />
      </div>
    );
  }

  return (
    <div className={style.tableContainer}>
      <Table.Root
        variant="surface"
        size="3"
        layout="fixed"
        className={style.override}
      >
        <Table.Header>
          {table.getHeaderGroups().map((headerGroup) => (
            <Table.Row key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Table.ColumnHeaderCell
                  key={header.id}
                  style={{ minWidth: header.getSize() }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </Table.ColumnHeaderCell>
              ))}
            </Table.Row>
          ))}
        </Table.Header>
        <Table.Body>
          {table.getRowModel().rows.map((row) => (
            <Table.Row key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <Table.Cell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </Table.Cell>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>
    </div>
  );
};
