"use client";
import { use, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON>, <PERSON>ing, Tabs } from "@radix-ui/themes";
import service from "@/api";
import { FiatDepositDetailsResponse } from "@/api/data-contracts";
import { SearchFilters } from "./_components/search-filters";
import { FundsDepositTable } from "./_components/funds-deposit-table";
import { Pagination } from "@repo/ui/pagination";
import { DownloadIcon } from "@radix-ui/react-icons";
import { InfoLayout } from "@repo/ui/info-layout";
import { Drawer } from "@repo/ui/drawer";
import { DepositReviewDialogContext } from "./_components/deposit-review/context";
import { DepositReviewDialogProvider } from "./_components/deposit-review";
import {
  DepositDetails,
  DepositDetailsHeader,
} from "./_components/deposit-details";
import { MediaDialogProvider } from "../../../components/media-dialog";
import { DepositRecord } from "./types";

const Page = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [activeTab, setActiveTab] = useState("fiat");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedDeposit, setSelectedDeposit] = useState<DepositRecord | null>(
    null,
  );
  const [drawerOpen, setDrawerOpen] = useState(false);
  const pageSize = 10;

  const { setSubmission } = use(DepositReviewDialogContext);

  // Handler for viewing deposit details
  const handleViewDetails = (record: DepositRecord) => {
    setSelectedDeposit(record);
    setDrawerOpen(true);
  };

  // Handler for closing drawer
  const handleCloseDrawer = () => {
    setDrawerOpen(false);
    setSelectedDeposit(null);
  };

  // Fetch fiat deposits
  const {
    data: fiatData,
    isLoading: fiatLoading,
    error: fiatError,
  } = useQuery({
    queryKey: [
      "fiat-deposits",
      searchQuery,
      statusFilter,
      currentPage,
      pageSize,
    ],
    queryFn: async () => {
      const searchParams: {
        userPublicId?: string;
        userEmail?: string;
        transactionId?: string;
        depositStatus?: "PENDING" | "APPROVED" | "REJECTED";
        page: number;
        pageSize: number;
      } = {
        page: currentPage,
        pageSize,
      };

      // Determine if search query is email, portfolio ID, or transaction ID
      if (searchQuery.trim()) {
        if (searchQuery.includes("@")) {
          searchParams.userEmail = searchQuery.trim();
        } else if (searchQuery.includes("-")) {
          // Assume it's a transaction ID if it contains hyphens
          searchParams.transactionId = searchQuery.trim();
        } else {
          searchParams.userPublicId = searchQuery.trim();
        }
      }

      // Add status filter if selected
      if (statusFilter) {
        searchParams.depositStatus = statusFilter as
          | "PENDING"
          | "APPROVED"
          | "REJECTED";
      }

      const response = await service.getFiatDepositsInfo(searchParams);
      return response.data?.data;
    },
  });

  const {
    data: cryptoData,
    isLoading: cryptoLoading,
    error: cryptoError,
  } = useQuery({
    queryKey: [
      "crypto-deposits",
      searchQuery,
      statusFilter,
      currentPage,
      pageSize,
    ],
    queryFn: async () => {
      const searchParams: {
        userPublicId?: string;
        userEmail?: string;
        transactionId?: string;
        depositStatus?: "PENDING" | "APPROVED" | "REJECTED";
        page: number;
        pageSize: number;
      } = {
        page: currentPage,
        pageSize,
      };

      // Determine if search query is email, portfolio ID, or transaction ID
      if (searchQuery.trim()) {
        if (searchQuery.includes("@")) {
          searchParams.userEmail = searchQuery.trim();
        } else if (searchQuery.includes("-")) {
          // Assume it's a transaction ID if it contains hyphens
          searchParams.transactionId = searchQuery.trim();
        } else {
          searchParams.userPublicId = searchQuery.trim();
        }
      }

      // Add status filter if selected
      if (statusFilter) {
        searchParams.depositStatus = statusFilter as
          | "PENDING"
          | "APPROVED"
          | "REJECTED";
      }

      const response = await service.getCryptoDepositsInfo(searchParams);
      return response.data?.data;
    },
  });

  // Helper function to get records with deposit type
  const getRecordsWithType = (
    data: { records?: FiatDepositDetailsResponse[] } | undefined,
    type: "FIAT" | "CRYPTO",
  ) => {
    const records = data?.records || [];
    return records.map((record: FiatDepositDetailsResponse) => ({
      ...record,
      depositType: type,
    }));
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleReset = () => {
    setCurrentPage(1); // Reset to first page when resetting
  };

  const handleExportData = () => {
    // TODO: Implement export functionality
    console.log("Export data functionality to be implemented");
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setCurrentPage(1); // Reset to first page when changing tabs
  };

  return (
    <main className="grow">
      <div className="flex justify-between items-center mb-6">
        <Heading size="7" weight="medium">
          Funds deposit
        </Heading>
        <Button
          radius="full"
          color="gray"
          highContrast
          variant="soft"
          size="2"
          onClick={handleExportData}
        >
          <DownloadIcon />
          Export data
        </Button>
      </div>

      <Tabs.Root value={activeTab} onValueChange={handleTabChange}>
        <Tabs.List className="mb-6" color="gray" highContrast>
          <Tabs.Trigger value="fiat">Bank transfer</Tabs.Trigger>
          <Tabs.Trigger value="crypto">Cryptocurrency</Tabs.Trigger>
        </Tabs.List>

        <div className="mb-6">
          <SearchFilters
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            statusFilter={statusFilter}
            onStatusChange={setStatusFilter}
            onSearch={handleSearch}
            onReset={handleReset}
          />
        </div>

        <Tabs.Content value="fiat">
          {fiatError ? (
            <div className="bg-[#********] rounded-lg">
              <InfoLayout
                className="py-10 px-6"
                icon="/empty-file.png"
                iconAlt="no data"
                title="Error"
                description="An error occurred while fetching fiat deposit information"
              />
            </div>
          ) : (
            <>
              <FundsDepositTable
                data={getRecordsWithType(fiatData, "FIAT")}
                isLoading={fiatLoading}
                onDepositReview={setSubmission}
                onViewDetails={handleViewDetails}
              />
              {(fiatData?.totalPages || 0) > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={fiatData?.totalPages || 0}
                  onPageChange={setCurrentPage}
                  className="justify-end mt-6"
                  nextText="Next"
                  previousText="Previous"
                />
              )}
            </>
          )}
        </Tabs.Content>

        <Tabs.Content value="crypto">
          {cryptoError ? (
            <div className="bg-[#********] rounded-lg">
              <InfoLayout
                className="py-10 px-6"
                icon="/empty-file.png"
                iconAlt="no data"
                title="Error"
                description="An error occurred while fetching crypto deposit information"
              />
            </div>
          ) : (
            <>
              <FundsDepositTable
                data={getRecordsWithType(cryptoData, "CRYPTO")}
                isLoading={cryptoLoading}
                onDepositReview={setSubmission}
                onViewDetails={handleViewDetails}
              />
              {(cryptoData?.totalPages || 0) > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={cryptoData?.totalPages || 0}
                  onPageChange={setCurrentPage}
                  className="justify-end mt-6"
                  nextText="Next"
                  previousText="Previous"
                />
              )}
            </>
          )}
        </Tabs.Content>
      </Tabs.Root>

      {/* Deposit Details Drawer */}
      <Drawer
        open={drawerOpen}
        onOpenChange={handleCloseDrawer}
        title={
          selectedDeposit && (
            <DepositDetailsHeader
              data={selectedDeposit}
              onDepositReview={setSubmission}
            />
          )
        }
      >
        {selectedDeposit && <DepositDetails data={selectedDeposit} />}
      </Drawer>
    </main>
  );
};

export default function FundsDepositPage() {
  return (
    <DepositReviewDialogProvider>
      <MediaDialogProvider>
        <Page />
      </MediaDialogProvider>
    </DepositReviewDialogProvider>
  );
}
