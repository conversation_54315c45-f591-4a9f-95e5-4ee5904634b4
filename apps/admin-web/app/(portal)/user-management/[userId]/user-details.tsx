import service from "@/api";
import { Container } from "@repo/ui/layout/container";
import { useQuery } from "@tanstack/react-query";

export const UserDetails = ({ userId }: { userId: string }) => {
  const { data } = useQuery({
    queryKey: ["user-details", userId],
    queryFn: async () => {
      const res = await service.getUsers({ publicId: userId });
      return res.data?.data;
    },
  });

  return (
    <Container>
      <div className="col-span-full">User details</div>
      <div className="col-span-full">{JSON.stringify(data)}</div>
    </Container>
  );
};
