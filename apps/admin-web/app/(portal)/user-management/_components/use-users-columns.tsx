import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { UserSummaryResponse } from "@/api/data-contracts";
import { Text } from "@radix-ui/themes";
import { CopyButton } from "@/ui-components/copy-button";
import { AmountDisplay } from "@repo/ui/amount-display";
import { formatDate } from "@/utils/date";
import Link from "next/link";

const columnHelper = createColumnHelper<UserSummaryResponse>();

export const useUsersColumns = () => {
  const columns: ColumnDef<UserSummaryResponse, any>[] = [
    columnHelper.accessor("publicId", {
      header: "Portfolio ID",
      cell: (info) => {
        const value = info.getValue();
        return (
          <div className="flex justify-between gap-2">
            <Text className="text-nowrap" color="orange" highContrast asChild>
              <Link href={`/user-management/${value}`}>{value || "-"}</Link>
            </Text>
            <CopyButton text={value} />
          </div>
        );
      },
      size: 250,
    }),
    columnHelper.accessor("email", {
      header: "Email",
      cell: (info) => {
        const value = info.getValue();
        return (
          <div className="flex justify-between gap-2">
            <Text>{value || "-"}</Text>
            <CopyButton text={value} />
          </div>
        );
      },
      size: 250,
    }),
    columnHelper.accessor("createdAt", {
      header: "Account creation time",
      cell: (info) => {
        const value = info.getValue();
        return <Text>{formatDate(value)}</Text>;
      },
      size: 200,
    }),
    columnHelper.accessor("totalInvestment", {
      header: "Total invested amount (USD)",
      cell: (info) => {
        const value = info.getValue();
        return <AmountDisplay amount={value?.amount} />;
      },
      size: 250,
    }),
    columnHelper.accessor("countryOfResidence", {
      header: "Country of residence",
      cell: (info) => {
        const value = info.getValue();
        return <Text>{value || "-"}</Text>;
      },
      size: 180,
    }),
  ];

  return columns;
};
