"use client";
import service from "@/api";
import { Heading, Card, Text, Table } from "@radix-ui/themes";
import { Container } from "@repo/ui/layout/container";
import { useQuery } from "@tanstack/react-query";
import { LoadingPlaceholder } from "@repo/ui/loading-placeholder";
import { InfoLayout } from "@repo/ui/info-layout";

// Tree map algorithm
interface TreeMapNode {
  asset: string;
  value: number;
  x: number;
  y: number;
  width: number;
  height: number;
}

const calculateTreeMap = (
  data: Array<{ asset?: string; percentage?: number }>,
  width: number,
  height: number,
): TreeMapNode[] => {
  const sortedData = data
    .filter((item) => item.percentage && item.percentage > 0)
    .sort((a, b) => (b.percentage || 0) - (a.percentage || 0))
    .map((item) => ({
      asset: item.asset || "",
      value: item.percentage || 0,
    }));

  if (sortedData.length === 0) return [];

  const nodes: TreeMapNode[] = [];

  // Recursive tree map layout function
  const layoutRectangles = (
    items: Array<{ asset: string; value: number }>,
    x: number,
    y: number,
    w: number,
    h: number,
  ) => {
    if (items.length === 0) return;

    if (items.length === 1) {
      nodes.push({
        asset: items[0]!.asset,
        value: items[0]!.value,
        x,
        y,
        width: w,
        height: h,
      });
      return;
    }

    // Calculate total value for current items
    const currentTotal = items.reduce((sum, item) => sum + item.value, 0);

    // Find the best split point
    let bestRatio = Infinity;
    let bestSplit = 1;

    for (let i = 1; i < items.length; i++) {
      const leftValue = items
        .slice(0, i)
        .reduce((sum, item) => sum + item.value, 0);
      const rightValue = currentTotal - leftValue;
      const ratio = Math.max(leftValue / rightValue, rightValue / leftValue);

      if (ratio < bestRatio) {
        bestRatio = ratio;
        bestSplit = i;
      }
    }

    const leftItems = items.slice(0, bestSplit);
    const rightItems = items.slice(bestSplit);
    const leftValue = leftItems.reduce((sum, item) => sum + item.value, 0);
    const leftRatio = leftValue / currentTotal;

    // Decide whether to split horizontally or vertically
    if (w >= h) {
      // Split vertically (left and right)
      const leftWidth = w * leftRatio;
      layoutRectangles(leftItems, x, y, leftWidth, h);
      layoutRectangles(rightItems, x + leftWidth, y, w - leftWidth, h);
    } else {
      // Split horizontally (top and bottom)
      const topHeight = h * leftRatio;
      layoutRectangles(leftItems, x, y, w, topHeight);
      layoutRectangles(rightItems, x, y + topHeight, w, h - topHeight);
    }
  };

  layoutRectangles(sortedData, 0, 0, width, height);
  return nodes;
};

const TreeMapChart = ({
  allocations,
  colors,
}: {
  allocations: Array<{ asset?: string; percentage?: number }>;
  colors: string[];
}) => {
  // Use normalized dimensions (0-100) for percentage calculations
  const normalizedWidth = 100;
  const normalizedHeight = 100;

  const nodes = calculateTreeMap(
    allocations,
    normalizedWidth,
    normalizedHeight,
  );

  return (
    <div className="relative bg-gray-50 h-[265px] overflow-hidden -m-2">
      {nodes.map((node, index) => (
        <div
          key={node.asset}
          className="absolute border border-white border-8 rounded-[16px] flex flex-col items-center justify-center font-medium"
          style={{
            left: `${node.x}%`,
            top: `${node.y}%`,
            width: `${node.width}%`,
            height: `${node.height}%`,
            backgroundColor: colors[index === 0 ? 0 : 1],
          }}
        >
          <Text
            size="5"
            weight="bold"
            className="text-center absolute top-4 left-4"
          >
            {node.asset}
          </Text>
          <Text
            size={index === 0 ? "7" : "5"}
            weight="bold"
            className="text-center absolute bottom-4 left-4"
          >
            {node.value.toFixed(1)}%
          </Text>
        </div>
      ))}
    </div>
  );
};

// Components
const TotalInvestmentsCard = ({
  totalAmount,
  currency,
}: {
  totalAmount?: number;
  currency?: string;
}) => {
  const formatAmount = (amount?: number) => {
    if (!amount) return "0";
    return (amount / 1000).toFixed(2) + "K";
  };

  return (
    <Card size="3">
      <div className="flex flex-col gap-4">
        <Heading size="5" weight="medium">
          Total investments
        </Heading>
        <div className="flex items-center gap-2">
          <Text size="8" weight="bold" className="text-[#EF630D]">
            {formatAmount(totalAmount)}
          </Text>
          <Text size="3" weight="medium">
            {currency}
          </Text>
        </div>
      </div>
    </Card>
  );
};

const AllocationCard = ({
  allocations,
}: {
  allocations?: Array<{ asset?: string; percentage?: number }>;
}) => {
  if (!allocations || allocations.length === 0) {
    return (
      <Card size="3" variant="surface">
        <div className="flex flex-col gap-4">
          <Heading size="5" weight="medium">
            Allocation
          </Heading>
          <div className="flex items-center justify-center h-48">
            <InfoLayout
              icon="/empty-file.png"
              iconAlt="no investment"
              title="No investment made yet"
              description=""
              className="text-center"
            />
          </div>
        </div>
      </Card>
    );
  }

  // Colors for tree map
  const colors = ["#F76B15BF", "#F2F2F2", "#F2F2F2"];

  return (
    <Card size="3">
      <div className="flex flex-col gap-4">
        <Heading size="5" weight="medium">
          Allocation
        </Heading>
        {/* Tree map visualization */}
        <TreeMapChart allocations={allocations} colors={colors} />
      </div>
    </Card>
  );
};

const BreakdownTable = ({
  breakdown,
}: {
  breakdown?: Array<{
    asset?: string;
    totalAmount?: { amount?: number; currency?: string };
    allocationPercentage?: number;
  }>;
}) => {
  if (!breakdown || breakdown.length === 0) {
    return (
      <Card size="3">
        <div className="flex flex-col gap-4">
          <Heading size="5" weight="medium">
            Breakdown
          </Heading>
          <InfoLayout
            icon="/empty-file.png"
            iconAlt="no records"
            title="No records found"
            description="Asset breakdown will be displayed once an investment inflow is recorded"
            className="py-12"
          />
        </div>
      </Card>
    );
  }

  return (
    <Card size="3">
      <div className="flex flex-col gap-4">
        <Heading size="5" weight="medium">
          Breakdown
        </Heading>
        <Table.Root size="3">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Asset</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>
                Total amount (USD)
              </Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Allocation</Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {breakdown.map((item, index) => (
              <Table.Row key={index}>
                <Table.Cell>
                  <Text weight="medium">{item.asset}</Text>
                </Table.Cell>
                <Table.Cell>
                  <Text>{item.totalAmount?.amount?.toLocaleString()}</Text>
                </Table.Cell>
                <Table.Cell>
                  <Text>{item.allocationPercentage?.toFixed(2)}%</Text>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </div>
    </Card>
  );
};

export default function PortfolioOverviewPage() {
  const { data: portfolioData, isLoading } = useQuery({
    queryFn: async () => {
      const res = await service.getPortfolioOverview();
      return res.data?.data;
    },
    queryKey: ["portfolio-overview"],
  });

  if (isLoading) {
    return (
      <main className="grow py-6 md:py-14">
        <Container>
          <div className="col-span-full">
            <LoadingPlaceholder />
          </div>
        </Container>
      </main>
    );
  }

  return (
    <main className="grow py-6 md:py-14">
      <Container>
        <div className="col-span-full flex flex-col gap-6">
          <Heading size="7" weight="medium">
            Portfolio overview
          </Heading>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TotalInvestmentsCard
              totalAmount={portfolioData?.totalInvestments?.totalAmount?.amount}
              currency={portfolioData?.totalInvestments?.totalAmount?.currency}
            />
            <AllocationCard allocations={portfolioData?.allocation} />
          </div>

          <BreakdownTable breakdown={portfolioData?.breakdown} />
        </div>
      </Container>
    </main>
  );
}
