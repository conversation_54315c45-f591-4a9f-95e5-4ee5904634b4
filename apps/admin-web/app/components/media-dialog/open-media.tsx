import service from "@/api";
import { EyeOpenIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";

export const OpenMediaButton = ({
  url,
  type,
}: {
  url: string;
  type: "image" | "pdf";
}) => {
  const handleOpen = async () => {
    const res = await service.getDocumentPresignedUrl({ fileKey: url });
    const presignedUrl = res.data?.data?.url ?? url;
    window.open(presignedUrl, "_blank");
  };

  return (
    <Button
      size="1"
      color="gray"
      radius="full"
      variant="soft"
      highContrast
      onClick={handleOpen}
    >
      {type === "image" ? "Image" : "PDF"} <EyeOpenIcon />
    </Button>
  );
};
