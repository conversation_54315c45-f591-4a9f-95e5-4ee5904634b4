import Image from "next/image";
import style from "./index.module.scss";
import { Button } from "@radix-ui/themes";
import { API_BASE_URL } from "@/app/constants";

export const LoginScreen = () => {
  return (
    <div className={style.container}>
      <div className={style.loginCard}>
        <h1 className={style.text}>
          Hello,{" "}
          <Image
            className={style.logo}
            src="/login-logo.svg"
            alt="ocas logo"
            width={240}
            height={52}
          />{" "}
          welcomes you back
        </h1>

        <Button
          size="3"
          radius="full"
          highContrast
          onClick={() => {
            window.location.href = `${API_BASE_URL}/api/v1/auth/login`;
          }}
        >
          Login
        </Button>
      </div>
    </div>
  );
};
