/**
 * Formats a date string to a consistent format across the admin application
 * @param dateString - The date string to format (ISO string or any valid date string)
 * @returns Formatted date string in DD/MM/YY HH:MM:SS format, or "-" if invalid/empty
 */
export const formatDate = (dateString?: string) => {
  if (!dateString) return "-";
  
  try {
    const date = new Date(dateString);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return dateString;
    }
    
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  } catch {
    return dateString;
  }
};
