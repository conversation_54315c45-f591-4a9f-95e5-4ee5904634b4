import { Text } from "@radix-ui/themes";
import { CopyButton } from "../copy-button";
import classNames from "classnames";

interface DetailRowProps {
  label: string;
  value: string | number | React.ReactNode;
  enableCopy?: boolean;
  copyText?: string;
}

export const DetailItem = ({
  label,
  value,
  enableCopy,
  copyText,
}: DetailRowProps) => {
  return (
    <div className="flex flex-col gap-1 items-start">
      <Text size="2" color="gray">
        {label}
      </Text>
      <div className="flex gap-2 items-center min-w-0 flex-1 justify-end">
        <Text size="2">{value}</Text>
        {enableCopy && copyText && <CopyButton text={copyText} />}
      </div>
    </div>
  );
};

export const DetailGrid = ({
  children,
  columns = 4,
}: {
  children: React.ReactNode;
  columns?: number;
}) => {
  return (
    <div
      className={classNames(
        "grid gap-y-4 gap-x-6",
        columns === 2
          ? "grid-cols-1 lg:grid-cols-2"
          : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
      )}
    >
      {children}
    </div>
  );
};
